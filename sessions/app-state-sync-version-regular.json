{"version": 5, "hash": {"type": "<PERSON><PERSON><PERSON>", "data": "J9HFeMPTrjPrldYerUeaJXqipRSW7MlkRjPATFCKX2wF5kj5dsETz8USstUjsOGFUlONdq/4dr3cMG/neDCww56rOGfeAnAEKoknRETP3zg++bOSSzVs4S/0+yfJLYGagA6Qa9S4dMM2jbZTwX7wfcQDPe/HBK5QWlwzWHBLLtY="}, "indexValueMap": {"FOnmUUOvpWq2+ZljhHdckn7LdzQkpJE/Ie3FMRO1ETE=": {"valueMac": {"type": "<PERSON><PERSON><PERSON>", "data": "7mxZ2iOt00gy2+6gZVevREF/4VWI++o/nwEc3BER5PI="}}, "LJrShd2PI5R8pLYn0+bQLzAd+MForVMEWWkYbX9ygYg=": {"valueMac": {"type": "<PERSON><PERSON><PERSON>", "data": "LFg9yavVF/yj9IEdf0/djiWTAb1GdCEOZtI1zJrZHJI="}}, "NEuOB4BaXm4wXnKdFePmU58vqZvY+6HQpN4rDrsJbXQ=": {"valueMac": {"type": "<PERSON><PERSON><PERSON>", "data": "IZnfbYAXSn7at2lO6CdW0nBpHutFh1OAhx5NUPF2rdQ="}}, "TEdjjPFrCfaqVoMdg7GvFLJ88Ae6rk7j1iA3HW0bZm0=": {"valueMac": {"type": "<PERSON><PERSON><PERSON>", "data": "F6MhKxDWtLSUv/6sTGjB52CY60rK1IkgaGBGnp90rwU="}}, "WQiY0/OgcuhAjPzt+sUjmhzL+F05wXVKZYgvKQs/0tQ=": {"valueMac": {"type": "<PERSON><PERSON><PERSON>", "data": "rZN3zigGBxy79RY1zrKgU1eBEV3ShbH7PfFJsD2Mh2I="}}, "q/dbXDM/H9+Oah/h/WPKBwuPer75Lh5gHRcGZvatqW4=": {"valueMac": {"type": "<PERSON><PERSON><PERSON>", "data": "Xe+QagmADgg5rFVAqY7mWeQcYAUV7zESWHnmEMx5mCI="}}, "zELmLQDZ3iWzN4hqlsTEIff5HTy+rDu9bovtHGOQL3E=": {"valueMac": {"type": "<PERSON><PERSON><PERSON>", "data": "mCCmkkNwuhKW7u5I1krAgpM8sPcvOA2L+gnB0nPKg5M="}}, "5FfahORYKO4G3IPgKVjRyefmzky7xeWY4ejbxpTruSQ=": {"valueMac": {"type": "<PERSON><PERSON><PERSON>", "data": "m8NsOWgRHh+PelGdftYKmG6xTOTocg2NMRrQmd88dBI="}}, "9l2ITAFuh0hH2PW/3jH86cKnTnb5HMtGejUMVxafPBs=": {"valueMac": {"type": "<PERSON><PERSON><PERSON>", "data": "ynBI0FAGfJCSUOID2Z+IjdV3+zFmdXnizDy4mnp5ePI="}}}}