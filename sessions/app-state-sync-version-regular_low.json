{"version": 49, "hash": {"type": "<PERSON><PERSON><PERSON>", "data": "Gg4IcO6jiGhemAKhbbfy0Dm7KZ8+8R10RsXArLb8ev2eAmWbdOS4+2r+Ak4fUYRFJ5SCgoKrZTpt5sp6MXFAHyevvWUcmIQdcX8asttNzfw7izSJJzafrT4jYtCOjiJFC3F9dy3AGal5v4FTDvTmqKlPr9e6dBGgQ2J+MzpY/uk="}, "indexValueMap": {"HYxLyDzKrqFwpwM19zQAC95IOnr6ZXIgL7eCbxQ0n3Y=": {"valueMac": {"type": "<PERSON><PERSON><PERSON>", "data": "T/oOb3saHIZ8jQWegtq0HjQf161udMjmR4Nxd8ZOSOk="}}, "NMIcYWJc14iUOjwsd8eGX/5yOif5ont+noOrV+wVTP0=": {"valueMac": {"type": "<PERSON><PERSON><PERSON>", "data": "Ans4knDYrdGjB6YKOIrWrZ8xapwxYnSLupEmgzwBNJ0="}}, "Txz1UO+Niuy8AVTMY0WlHzM6Gl1wQjcu8WtQhjwFt24=": {"valueMac": {"type": "<PERSON><PERSON><PERSON>", "data": "zg4CRFv+FAG03Hcs7tTv2tiGvZw+E3qD8LLvupzt5C4="}}, "Vy++cKvNoULKtBGDQrt+p13FA1MmBKfIT/qRiuYkeeU=": {"valueMac": {"type": "<PERSON><PERSON><PERSON>", "data": "1jmwhLgnzsW0fe3MI1BPaf9PilVWK3PrZfEV6lhMrHw="}}, "WWoS5lwEXR0UGaADTnUo/u1j8UcHxMm2vrVr8culdD0=": {"valueMac": {"type": "<PERSON><PERSON><PERSON>", "data": "sPtbH7WrciWKSLr14T2PXyF33IKhnQfgvoRQIHObmko="}}, "XHOjhTrpMyu168UHYGsRdclP4mdCS2sDmP4KQZ13x8Q=": {"valueMac": {"type": "<PERSON><PERSON><PERSON>", "data": "YNuaYDLF/MMJTbn9SZTG1wDjp8wMhabbYUFe/9rimQE="}}, "Zizhpfg9vfr4Zd7rx7Fctjbz0QVbv4nPisfzj5i2PP8=": {"valueMac": {"type": "<PERSON><PERSON><PERSON>", "data": "vWH013RHFB5zXyTTYVh0cZPkmkB+fHRShkaa6TveHV8="}}, "c6f1CKKEq/3MIPWBEiCyrlZTEJdY8xzVr5Q9kyECF/w=": {"valueMac": {"type": "<PERSON><PERSON><PERSON>", "data": "DaGoOM8MIn2v5FKYAYiGGyriGtEugCWjRGkZSDxapLA="}}, "iUVV/7biIVrGVMGpwZro5jmV4TAhEcKU8DuIi/iAS6E=": {"valueMac": {"type": "<PERSON><PERSON><PERSON>", "data": "sOW3ztmlXzhx/KCvzeJwpvOWqqGfkyIdm4oSt9L8AJM="}}, "osBVJnjd14PoYx7oovEv1xfXp4rZw1JcPhsyvrlalmw=": {"valueMac": {"type": "<PERSON><PERSON><PERSON>", "data": "CvkFX8gxSyDTvRR/ePDgAEhlZ0vP6EKrePJClDVXYEg="}}, "71iMb1PDz+OQvWl2yjxWCA6YS6BOlabu2k1PlvME4VY=": {"valueMac": {"type": "<PERSON><PERSON><PERSON>", "data": "UF7QItH7JVwHUO7kncdw70dBiuL55dFFokdzmMGjE9Y="}}}}