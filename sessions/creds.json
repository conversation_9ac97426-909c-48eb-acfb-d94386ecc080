{"noiseKey": {"private": {"type": "<PERSON><PERSON><PERSON>", "data": "cKaSrr8Kkm0UPVGoBJOe1DM7gXjIfzentZh1A7EFLHs="}, "public": {"type": "<PERSON><PERSON><PERSON>", "data": "28lx3wZ2HU9W+npVubXLyuCrPRXbEjrssJG9oCC0LTk="}}, "pairingEphemeralKeyPair": {"private": {"type": "<PERSON><PERSON><PERSON>", "data": "CCZ61P8g2c0Seu67bopA1hohZpJ3v+VrU8FtIBo65Hs="}, "public": {"type": "<PERSON><PERSON><PERSON>", "data": "N6vzWctrbN9AKi/uIDcwQWlCa4yXtb6Mp+siP63EHgk="}}, "signedIdentityKey": {"private": {"type": "<PERSON><PERSON><PERSON>", "data": "qBhyM0XBdYrM1pIK5ivTwCDWpTpaMs/9WcibUCIwbFk="}, "public": {"type": "<PERSON><PERSON><PERSON>", "data": "C72QLflOCppa6KDaghR5DKWtQJUGhplZX4+HwXU0NA0="}}, "signedPreKey": {"keyPair": {"private": {"type": "<PERSON><PERSON><PERSON>", "data": "+D4o7vThYcUDuw0Na7fmi3yRmE9JKgk0iEyJr0J5PHE="}, "public": {"type": "<PERSON><PERSON><PERSON>", "data": "WMD2SzTGoBtKvFvS9gJH8JqbySJdRDfVdhl1A5vO11k="}}, "signature": {"type": "<PERSON><PERSON><PERSON>", "data": "mizFdMQnYveiwiqQ5I72zKIZ8Y3HGHaibSkWa4zkjWAraUkNlIAqXaP6FdGAfYcspjXm4Ly6l6GAzdf09dDohA=="}, "keyId": 1}, "registrationId": 137, "advSecretKey": "8T2kHBXN/jzV5j9vyzvGneWBEwJGMUGAS3OMzMED6hI=", "processedHistoryMessages": [{"key": {"remoteJid": "<EMAIL>", "fromMe": true, "id": "3A8B71331CC6C7C1263B"}, "messageTimestamp": **********}, {"key": {"remoteJid": "<EMAIL>", "fromMe": true, "id": "3A62CCE0F791622949DE"}, "messageTimestamp": **********}, {"key": {"remoteJid": "<EMAIL>", "fromMe": true, "id": "3A0508D2F2E7699CFAF3"}, "messageTimestamp": **********}], "nextPreKeyId": 31, "firstUnuploadedPreKeyId": 31, "accountSyncCounter": 1, "accountSettings": {"unarchiveChats": false}, "registered": false, "account": {"details": "COu56rUEEJOGv8QGGAQgACgA", "accountSignatureKey": "XVrAhAOLwd6m7AnsPytPnMTOjcGIGtpi1uzrkniFnHg=", "accountSignature": "Ld//zRqB06IF1GZ863Eggpwu/Qp7ZAd+n1yaHwZvzd48rkI+bOic303ba9F2DUwj230geKLlEiQHvHf4c0R+jw==", "deviceSignature": "oWXVFkST2Q9vrO56hT8mp9Kfhev0mCtTugYmROW0lA5HlD7V5gY98MPHI/sL8IfGb8RxZJYkdrUQj54BGEGVjg=="}, "me": {"id": "************:<EMAIL>", "lid": "***************:5@lid", "name": "<PERSON><PERSON>"}, "signalIdentities": [{"identifier": {"name": "************:<EMAIL>", "deviceId": 0}, "identifierKey": {"type": "<PERSON><PERSON><PERSON>", "data": "BV1awIQDi8HepuwJ7D8rT5zEzo3BiBraYtbs65J4hZx4"}}], "platform": "iphone", "routingInfo": {"type": "<PERSON><PERSON><PERSON>", "data": "CA0ICA=="}, "lastAccountSyncTimestamp": **********, "lastPropHash": "2V77qU", "myAppStateKeyId": "AAAAAHSb"}