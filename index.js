// Carrega o polyfill de crypto ANTES de qualquer outra coisa
require('./src/utils/crypto-polyfill');

const { startWhatsAppBot } = require('./src/whatsapp/client');
const logger = require('./src/utils/logger');

async function main() {
    try {
        logger.info('🚀 Iniciando WhatsApp Bot com Botões...');
        await startWhatsAppBot();
    } catch (error) {
        logger.error('❌ Erro ao iniciar o bot:', error);
        process.exit(1);
    }
}

// Tratamento de erros não capturados
process.on('uncaughtException', (error) => {
    logger.error('Erro não capturado:', error);
    process.exit(1);
});

process.on('unhandledRejection', (reason, promise) => {
    logger.error('Promise rejeitada não tratada:', reason);
    process.exit(1);
});

// Iniciar o bot
main();
