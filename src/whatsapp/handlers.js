const logger = require('../utils/logger');
const { createQuickReplyButtons, createListMessage, createButtonMessage } = require('./buttons');

/**
 * Manipula mensagens recebidas e respostas de botões
 */
async function handleMessages(sock, messageUpdate) {
    const { messages } = messageUpdate;
    
    for (const message of messages) {
        // Ignora mensagens próprias
        if (message.key.fromMe) continue;
        
        // Ignora mensagens de status
        if (message.key.remoteJid === 'status@broadcast') continue;
        
        const messageType = Object.keys(message.message || {})[0];
        const from = message.key.remoteJid;
        const messageId = message.key.id;
        
        logger.info(`📨 Mensagem recebida de ${from}, tipo: ${messageType}`);
        
        try {
            // Manipula diferentes tipos de mensagem
            switch (messageType) {
                case 'conversation':
                    await handleTextMessage(sock, message, message.message.conversation);
                    break;
                    
                case 'extendedTextMessage':
                    await handleTextMessage(sock, message, message.message.extendedTextMessage.text);
                    break;
                    
                case 'buttonsResponseMessage':
                    await handleButtonResponse(sock, message);
                    break;
                    
                case 'listResponseMessage':
                    await handleListResponse(sock, message);
                    break;
                    
                case 'templateButtonReplyMessage':
                    await handleTemplateButtonResponse(sock, message);
                    break;
                    
                default:
                    logger.info(`Tipo de mensagem não tratado: ${messageType}`);
            }
        } catch (error) {
            logger.error('Erro ao processar mensagem:', error);
        }
    }
}

/**
 * Manipula mensagens de texto e comandos
 */
async function handleTextMessage(sock, message, text) {
    const from = message.key.remoteJid;
    const command = text.toLowerCase().trim();
    
    logger.info(`💬 Texto recebido: "${text}"`);
    
    // Comandos para demonstrar botões
    switch (command) {
        case '/botoes':
        case '/buttons':
            await sendQuickReplyExample(sock, from);
            break;
            
        case '/lista':
        case '/list':
            await sendListExample(sock, from);
            break;
            
        case '/menu':
            await sendMenuButtons(sock, from);
            break;
            
        case '/help':
        case '/ajuda':
            await sendHelpMessage(sock, from);
            break;
            
        default:
            // Resposta padrão para mensagens não reconhecidas
            await sock.sendMessage(from, {
                text: `Olá! 👋\n\nRecebemos sua mensagem: "${text}"\n\nDigite /help para ver os comandos disponíveis.`
            });
    }
}

/**
 * Manipula respostas de botões rápidos
 */
async function handleButtonResponse(sock, message) {
    const from = message.key.remoteJid;
    const buttonResponse = message.message.buttonsResponseMessage;
    const selectedButtonId = buttonResponse.selectedButtonId;
    const displayText = buttonResponse.selectedDisplayText;
    
    logger.info(`🔘 Botão clicado: ${selectedButtonId} (${displayText})`);
    
    // Processa diferentes respostas de botão
    switch (selectedButtonId) {
        case 'start_bot':
            await sock.sendMessage(from, {
                text: '🎉 *Bot iniciado com sucesso!*\n\n' +
                      '📋 *Menu de opções:*\n\n' +
                      '🔘 Digite `/botoes` para ver botões interativos\n' +
                      '📋 Digite `/lista` para ver listas dinâmicas\n' +
                      '🎯 Digite `/menu` para menu principal\n' +
                      '❓ Digite `/help` para ajuda completa\n\n' +
                      '✨ Ou envie qualquer mensagem para interagir!'
            });
            break;

        case 'start_demo':
            await sendQuickReplyExample(sock, from);
            break;

        case 'show_menu':
            await sendListExample(sock, from);
            break;

        case 'get_help':
            await sendHelpMessage(sock, from);
            break;

        case 'option_1':
            await sock.sendMessage(from, {
                text: `✅ Você escolheu a Opção 1!\n\nAqui está mais informação sobre esta opção...`
            });
            break;

        case 'option_2':
            await sock.sendMessage(from, {
                text: `✅ Você escolheu a Opção 2!\n\nProcessando sua solicitação...`
            });
            break;

        case 'option_3':
            await sock.sendMessage(from, {
                text: `✅ Você escolheu a Opção 3!\n\nRedirecionando para suporte...`
            });
            break;
            
        case 'confirm_yes':
            await sock.sendMessage(from, {
                text: `✅ Confirmado! Sua ação foi processada com sucesso.`
            });
            break;
            
        case 'confirm_no':
            await sock.sendMessage(from, {
                text: `❌ Ação cancelada. Como posso ajudá-lo de outra forma?`
            });
            break;
            
        default:
            await sock.sendMessage(from, {
                text: `Botão "${displayText}" foi clicado. ID: ${selectedButtonId}`
            });
    }
}

/**
 * Manipula respostas de listas
 */
async function handleListResponse(sock, message) {
    const from = message.key.remoteJid;
    const listResponse = message.message.listResponseMessage;
    const selectedRowId = listResponse.singleSelectReply.selectedRowId;
    const title = listResponse.title;
    
    logger.info(`📋 Item da lista selecionado: ${selectedRowId} (${title})`);
    
    // Processa diferentes seleções da lista
    switch (selectedRowId) {
        case 'demo_buttons':
            await sendQuickReplyExample(sock, from);
            break;

        case 'demo_lists':
            await sendListExample(sock, from);
            break;

        case 'demo_menu':
            await sendMenuButtons(sock, from);
            break;

        case 'get_help':
            await sendHelpMessage(sock, from);
            break;

        case 'contact_support':
            await sock.sendMessage(from, {
                text: `🎧 Conectando você ao suporte...\n\nUm de nossos atendentes entrará em contato em breve.\n\n📞 Telefone: (62) 98230-1217\n📧 Email: <EMAIL>`
            });
            break;

        case 'product_1':
            await sock.sendMessage(from, {
                text: `🛍️ Produto 1 selecionado!\n\nDetalhes do produto:\n- Nome: Produto Premium\n- Preço: R$ 99,90\n- Disponibilidade: Em estoque`
            });
            break;

        case 'product_2':
            await sock.sendMessage(from, {
                text: `🛍️ Produto 2 selecionado!\n\nDetalhes do produto:\n- Nome: Produto Standard\n- Preço: R$ 59,90\n- Disponibilidade: Em estoque`
            });
            break;

        case 'support':
            await sock.sendMessage(from, {
                text: `🎧 Conectando você ao suporte...\n\nUm de nossos atendentes entrará em contato em breve.`
            });
            break;
            
        default:
            await sock.sendMessage(from, {
                text: `Item selecionado: ${title} (ID: ${selectedRowId})`
            });
    }
}

/**
 * Manipula respostas de botões de template
 */
async function handleTemplateButtonResponse(sock, message) {
    const from = message.key.remoteJid;
    const templateResponse = message.message.templateButtonReplyMessage;
    const selectedId = templateResponse.selectedId;
    const displayText = templateResponse.selectedDisplayText;
    
    logger.info(`🎯 Botão de template clicado: ${selectedId} (${displayText})`);
    
    await sock.sendMessage(from, {
        text: `Botão de template "${displayText}" foi clicado. ID: ${selectedId}`
    });
}

/**
 * Envia exemplo de botões rápidos
 */
async function sendQuickReplyExample(sock, to) {
    const buttons = createQuickReplyButtons([
        { id: 'option_1', text: '🔥 Opção 1' },
        { id: 'option_2', text: '⭐ Opção 2' },
        { id: 'option_3', text: '💎 Opção 3' }
    ]);
    
    await sock.sendMessage(to, {
        text: '🎯 Escolha uma das opções abaixo:',
        buttons: buttons,
        headerType: 1
    });
}

/**
 * Envia exemplo de lista
 */
async function sendListExample(sock, to) {
    const listMessage = createListMessage(
        'Nossos Produtos',
        'Selecione um produto para ver mais detalhes',
        'Ver Produtos',
        [
            {
                title: 'Produtos Premium',
                rows: [
                    { id: 'product_1', title: 'Produto Premium', description: 'Nosso melhor produto - R$ 99,90' },
                    { id: 'product_2', title: 'Produto Standard', description: 'Ótimo custo-benefício - R$ 59,90' }
                ]
            },
            {
                title: 'Suporte',
                rows: [
                    { id: 'support', title: 'Falar com Suporte', description: 'Conecte-se com nossa equipe' }
                ]
            }
        ]
    );
    
    await sock.sendMessage(to, listMessage);
}

/**
 * Envia menu principal com botões
 */
async function sendMenuButtons(sock, to) {
    const buttons = createQuickReplyButtons([
        { id: 'confirm_yes', text: '✅ Sim' },
        { id: 'confirm_no', text: '❌ Não' }
    ]);
    
    await sock.sendMessage(to, {
        text: '🤖 *Menu Principal*\n\nDeseja continuar com o atendimento?',
        buttons: buttons,
        headerType: 1
    });
}

/**
 * Envia mensagem de ajuda
 */
async function sendHelpMessage(sock, to) {
    const helpText = `🤖 *Comandos Disponíveis:*

/botoes ou /buttons - Exemplo de botões rápidos
/lista ou /list - Exemplo de lista interativa  
/menu - Menu principal
/help ou /ajuda - Esta mensagem

*Como usar:*
• Digite qualquer comando acima
• Clique nos botões que aparecem
• Selecione itens das listas

*Exemplos de interação:*
• Envie uma mensagem qualquer para receber uma resposta
• Use os botões para navegar pelas opções
• Explore as diferentes funcionalidades!`;

    await sock.sendMessage(to, { text: helpText });
}

module.exports = {
    handleMessages,
    handleTextMessage,
    handleButtonResponse,
    handleListResponse,
    handleTemplateButtonResponse
};
