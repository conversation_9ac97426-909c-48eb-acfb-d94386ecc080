const { 
    default: makeWASocket, 
    DisconnectReason, 
    useMultiFileAuthState,
    fetchLatestBaileysVersion 
} = require('@whiskeysockets/baileys');
const qrcode = require('qrcode-terminal');
const logger = require('../utils/logger');
const { handleMessages } = require('./handlers');

async function startWhatsAppBot() {
    const { state, saveCreds } = await useMultiFileAuthState('./sessions');
    const { version, isLatest } = await fetchLatestBaileysVersion();
    
    logger.info(`Usando Baileys v${version.join('.')}, é a mais recente: ${isLatest}`);

    const sock = makeWASocket({
        version,
        auth: state,
        printQRInTerminal: false,
        logger: logger.child({ module: 'baileys' }),
        browser: ['WhatsApp Bot', 'Chrome', '1.0.0']
    });

    sock.ev.on('connection.update', (update) => {
        const { connection, lastDisconnect, qr } = update;
        
        if (qr) {
            logger.info('📱 Escaneie o QR Code:');
            qrcode.generate(qr, { small: true });
        }
        
        if (connection === 'close') {
            const shouldReconnect = lastDisconnect?.error?.output?.statusCode !== DisconnectReason.loggedOut;
            logger.info('Conexão fechada devido a:', lastDisconnect?.error);
            
            if (shouldReconnect) {
                logger.info('🔄 Reconectando...');
                startWhatsAppBot();
            }
        } else if (connection === 'open') {
            logger.info('✅ Conectado ao WhatsApp!');
        }
    });

    sock.ev.on('creds.update', saveCreds);
    sock.ev.on('messages.upsert', (m) => handleMessages(sock, m));

    return sock;
}

module.exports = { startWhatsAppBot };