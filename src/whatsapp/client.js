const { 
    default: makeWASocket, 
    DisconnectReason, 
    useMultiFileAuthState,
    fetchLatestBaileysVersion 
} = require('@neoxr/baileys');
const qrcode = require('qrcode-terminal');
const logger = require('../utils/logger');
const { handleMessages } = require('./handlers');
const { createQuickReplyButtons } = require('./buttons');

async function startWhatsAppBot() {
    const { state, saveCreds } = await useMultiFileAuthState('./sessions');
    const { version, isLatest } = await fetchLatestBaileysVersion();
    
    logger.info(`Usando Baileys v${version.join('.')}, é a mais recente: ${isLatest}`);

    const sock = makeWASocket({
        version,
        auth: state,
        printQRInTerminal: false,
        logger: logger.child({ module: 'baileys' }),
        browser: ['WhatsApp Bot', 'Chrome', '1.0.0']
    });

    sock.ev.on('connection.update', (update) => {
        const { connection, lastDisconnect, qr } = update;
        
        if (qr) {
            logger.info('📱 Escaneie o QR Code:');
            qrcode.generate(qr, { small: true });
        }
        
        if (connection === 'close') {
            const shouldReconnect = lastDisconnect?.error?.output?.statusCode !== DisconnectReason.loggedOut;
            logger.info('Conexão fechada devido a:', lastDisconnect?.error);
            
            if (shouldReconnect) {
                logger.info('🔄 Reconectando...');
                startWhatsAppBot();
            }
        } else if (connection === 'open') {
            logger.info('✅ Conectado ao WhatsApp!');

            // Envia mensagem com botões após conectar
            setTimeout(async () => {
                try {
                    const testNumber = '<EMAIL>';

                    // Cria botões de boas-vindas
                    const welcomeButtons = createQuickReplyButtons([
                        { id: 'start_demo', text: '🚀 Iniciar Demo' },
                        { id: 'show_menu', text: '📋 Ver Menu' },
                        { id: 'get_help', text: '❓ Ajuda' }
                    ]);

                    await sock.sendMessage(testNumber, {
                        text: '🤖 *Oi! Bot conectado com sucesso!* 🎉\n\n' +
                              'Bem-vindo ao teste de botões do WhatsApp!\n\n' +
                              '✨ *Funcionalidades disponíveis:*\n' +
                              '• Botões interativos\n' +
                              '• Listas de seleção\n' +
                              '• Menus dinâmicos\n\n' +
                              '👇 Escolha uma opção abaixo para começar:',
                        buttons: welcomeButtons,
                        headerType: 1
                    });

                    logger.info('✅ Mensagem com botões enviada!');
                } catch (error) {
                    logger.error('❌ Erro ao enviar mensagem com botões:', error);
                }
            }, 2000); // Aguarda 2 segundos após conectar
        }
    });

    sock.ev.on('creds.update', saveCreds);
    sock.ev.on('messages.upsert', (m) => handleMessages(sock, m));

    return sock;
}

module.exports = { startWhatsAppBot };