const {
    default: makeWASocket,
    DisconnectReason,
    useMultiFileAuthState,
    fetchLatestBaileysVersion,
    makeCacheableSignalKeyStore,
    proto
} = require('@neoxr/baileys');
const qrcode = require('qrcode-terminal');
const pino = require('pino');
const logger = require('../utils/logger');
const { handleMessages } = require('./handlers');
const { createListMessage } = require('./buttons');

// Variáveis globais para cache e store
let store;
const msgRetryCounterCache = new Map();
const usePairingCode = false; // Usar QR Code por padrão

async function startWhatsAppBot() {
    const { state, saveCreds } = await useMultiFileAuthState('./sessions');

    logger.info('Configurando opções de conexão avançadas...');

    const connectionOptions = {
        logger: pino({ level: "silent" }),
        version: (
            await (
                await fetch(
                    "https://raw.githubusercontent.com/WhiskeySockets/Baileys/master/src/Defaults/baileys-version.json"
                )
            ).json()
        ).version,
        browser: ["Ubuntu", "Chrome", "20.0.04"],
        printQRInTerminal: !usePairingCode,
        auth: {
            creds: state.creds,
            keys: makeCacheableSignalKeyStore(
                state.keys,
                pino().child({
                    level: "silent",
                    stream: "store"
                })
            )
        },
        isLatest: true,
        markOnlineOnConnect: true,
        generateHighQualityLinkPreview: true,
        patchMessageBeforeSending: message => {
            console.log(`vai enviar\n\n\n`);
            const requiresPatch = !!(
                message.buttonsMessage ||
                message.templateMessage ||
                message.listMessage
            );

            if (requiresPatch) {
                message = {
                    viewOnceMessage: {
                        message: {
                            messageContextInfo: {
                                deviceListMetadataVersion: 2,
                                deviceListMetadata: {}
                            },
                            ...message
                        }
                    }
                };
            }

            return message;
        },
        getMessage: async key => {
            if (store) {
                const msg = await store.loadMessage(key.remoteJid, key.id);
                return msg?.message || undefined;
            }
            return proto.Message.fromObject({});
        },
        msgRetryCounterCache,
        msgRetryCounterMap: {},
        shouldSyncHistoryMessage: msg => {
            console.log(`\x1b[32mMemuat chat [${msg.progress}%]\x1b[39m`);
            return !!msg.syncType;
        },
        syncFullHistory: true,
        connectTimeoutMs: 60000,
        defaultQueryTimeoutMs: 0,
        keepAliveIntervalMs: 10000
    };

    logger.info('Criando socket WhatsApp com configurações avançadas...');
    const sock = makeWASocket(connectionOptions);

    sock.ev.on('connection.update', (update) => {
        const { connection, lastDisconnect, qr } = update;
        
        if (qr) {
            logger.info('📱 Escaneie o QR Code:');
            qrcode.generate(qr, { small: true });
        }
        
        if (connection === 'close') {
            const shouldReconnect = lastDisconnect?.error?.output?.statusCode !== DisconnectReason.loggedOut;
            logger.info('Conexão fechada devido a:', lastDisconnect?.error);
            
            if (shouldReconnect) {
                logger.info('🔄 Reconectando...');
                startWhatsAppBot();
            }
        } else if (connection === 'open') {
            logger.info('✅ Conectado ao WhatsApp!');

            // Envia mensagem com lista após conectar
            setTimeout(async () => {
                try {
                    const testNumber = '<EMAIL>';

                    // Envia mensagem com 1 botão reply
                    const buttonMessage = {
                        text: '🤖 Oi! Bot conectado com sucesso! 🎉\n\n' +
                              'Bem-vindo ao teste de botões do WhatsApp!\n\n' +
                              '✨ Funcionalidades disponíveis:\n' +
                              '• Botões interativos\n' +
                              '• Listas de seleção\n' +
                              '• Menus dinâmicos\n\n' +
                              '👇 Clique no botão abaixo para começar:',
                            buttons: [
                            {
                                buttonId: 'start_bot',
                                buttonText: { displayText: '🚀 Iniciar Bot' },
                                type: 1
                            }
                        ]
                    };

                    const t = await sock.sendMessage(testNumber, buttonMessage);

                    console.log(t);

                    logger.info('✅ Mensagem com botão enviada!', t);
                } catch (error) {
                    logger.error('❌ Erro ao enviar mensagem com botão:', error);
                }
            }, 2000); // Aguarda 2 segundos após conectar
        }
    });

    sock.ev.on('creds.update', saveCreds);
    sock.ev.on('messages.upsert', (m) => handleMessages(sock, m));

    return sock;
}

module.exports = { startWhatsAppBot };