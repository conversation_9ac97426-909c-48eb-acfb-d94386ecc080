const { 
    default: makeWASocket, 
    DisconnectReason, 
    useMultiFileAuthState,
    fetchLatestBaileysVersion 
} = require('@neoxr/baileys');
const qrcode = require('qrcode-terminal');
const logger = require('../utils/logger');
const { handleMessages } = require('./handlers');
const { createListMessage } = require('./buttons');

async function startWhatsAppBot() {
    const { state, saveCreds } = await useMultiFileAuthState('./sessions');
    const { version, isLatest } = await fetchLatestBaileysVersion();
    
    logger.info(`Usando Baileys v${version.join('.')}, é a mais recente: ${isLatest}`);

    const sock = makeWASocket({
        version,
        auth: state,
        printQRInTerminal: false,
        logger: logger.child({ module: 'baileys' }),
        browser: ['WhatsApp Bot', 'Chrome', '1.0.0']
    });

    sock.ev.on('connection.update', (update) => {
        const { connection, lastDisconnect, qr } = update;
        
        if (qr) {
            logger.info('📱 Escaneie o QR Code:');
            qrcode.generate(qr, { small: true });
        }
        
        if (connection === 'close') {
            const shouldReconnect = lastDisconnect?.error?.output?.statusCode !== DisconnectReason.loggedOut;
            logger.info('Conexão fechada devido a:', lastDisconnect?.error);
            
            if (shouldReconnect) {
                logger.info('🔄 Reconectando...');
                startWhatsAppBot();
            }
        } else if (connection === 'open') {
            logger.info('✅ Conectado ao WhatsApp!');

            // Envia mensagem com botões após conectar
            setTimeout(async () => {
                try {
                    const testNumber = '<EMAIL>';

                    // send a buttons message!
                    const buttons = [
                        {buttonId: 'id1', buttonText: {displayText: 'Button 1'}, type: 1},
                        {buttonId: 'id2', buttonText: {displayText: 'Button 2'}, type: 1},
                        {buttonId: 'id3', buttonText: {displayText: 'Button 3'}, type: 1}
                    ]

                    const buttonMessage = {
                        text: "Hi it's button message",
                        footer: 'Hello World',
                        buttons: buttons,
                        headerType: 1
                    }

                    const sendMsg = await sock.sendMessage(testNumber, buttonMessage)

                    logger.info('✅ Mensagem com botões enviada!');
                } catch (error) {
                    console.log(error);
                    logger.error('❌ Erro ao enviar mensagem com botões:', error);
                }
            }, 2000); // Aguarda 2 segundos após conectar
        }
    });

    sock.ev.on('creds.update', saveCreds);
    sock.ev.on('messages.upsert', (m) => handleMessages(sock, m));

    return sock;
}

module.exports = { startWhatsAppBot };