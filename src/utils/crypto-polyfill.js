/**
 * Polyfill para crypto no Node.js
 * Resolve o problema com @neoxr/baileys e globalThis.crypto
 */

const crypto = require('crypto');

// Adiciona o polyfill para globalThis.crypto se não existir
if (!globalThis.crypto) {
    globalThis.crypto = {
        subtle: crypto.webcrypto?.subtle || {
            importKey: async () => { throw new Error('WebCrypto not available'); },
            exportKey: async () => { throw new Error('WebCrypto not available'); },
            encrypt: async () => { throw new Error('WebCrypto not available'); },
            decrypt: async () => { throw new Error('WebCrypto not available'); },
            sign: async () => { throw new Error('WebCrypto not available'); },
            verify: async () => { throw new Error('WebCrypto not available'); },
            digest: async () => { throw new Error('WebCrypto not available'); },
            generateKey: async () => { throw new Error('WebCrypto not available'); },
            deriveKey: async () => { throw new Error('WebCrypto not available'); },
            deriveBits: async () => { throw new Error('WebCrypto not available'); },
            wrapKey: async () => { throw new Error('WebCrypto not available'); },
            unwrapKey: async () => { throw new Error('WebCrypto not available'); }
        },
        getRandomValues: (array) => {
            return crypto.randomFillSync(array);
        }
    };
}

module.exports = {};
