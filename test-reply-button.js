// Teste para verificar se existe replyButton no @neoxr/baileys

try {
    const baileys = require('@neoxr/baileys');
    
    console.log('📦 Funções disponíveis no @neoxr/baileys:');
    console.log(Object.keys(baileys));
    
    // Verifica se existe replyButton
    if (baileys.replyButton) {
        console.log('✅ replyButton encontrado!');
        console.log('Tipo:', typeof baileys.replyButton);
    } else {
        console.log('❌ replyButton NÃO encontrado');
    }
    
    // Verifica outras funções relacionadas a botões
    const buttonFunctions = Object.keys(baileys).filter(key => 
        key.toLowerCase().includes('button') || 
        key.toLowerCase().includes('reply')
    );
    
    console.log('\n🔘 Funções relacionadas a botões/reply:');
    console.log(buttonFunctions);
    
} catch (error) {
    console.error('❌ Erro ao carregar @neoxr/baileys:', error.message);
}
